services:
  trojan-go-wss:
    image: teddysun/trojan-go:latest
    container_name: trojan-go-wss
    restart: unless-stopped
    network_mode: host
    volumes:
      - ./etc/trojan-go:/etc/trojan-go
      - ./logs/trojan-go:/var/log/trojan
      - ./etc/ssl:/etc/ssl/cloudflare/
    environment:
      - DOMAIN=${DOMAIN}
      - TROJAN_PASSWORD=${TROJAN_PASSWORD}
      - WEBSOCKET_PATH=${WEBSOCKET_PATH}
      - LOCAL_PORT=${LOCAL_PORT}
      - FALLBACK_PORT=${FALLBACK_PORT}
      - LOG_LEVEL=${LOG_LEVEL}
      - SSL_CERT=${SSL_CERT}
      - SSL_KEY=${SSL_KEY}
      - USER_ID=${USER_ID}
      - GROUP_ID=${GROUP_ID}
    command: ["/bin/sh", "/etc/trojan-go/start-trojan.sh"]
    depends_on:
      - trojan-caddy

  trojan-caddy:
    image: caddy:latest
    container_name: trojan-caddy
    restart: unless-stopped
    network_mode: host
    volumes:
      - ./etc/caddy:/etc/caddy
      - ./etc/caddy/caddy-data:/data
      - ./etc/caddy/caddy-config:/config
      - ./etc/caddy/html:/var/www/html
      - ./etc/certbot/www:/var/www/certbot
      - ./etc/ssl:/etc/ssl
      - ./logs/caddy:/var/log/caddy
    environment:
      - DOMAIN=${DOMAIN}
      - WEBSOCKET_PATH=${WEBSOCKET_PATH}
      - FALLBACK_PORT=${FALLBACK_PORT}
      - HTTPS_PORT=${HTTPS_PORT}
      - HEALTH_CHECK_PORT=${HEALTH_CHECK_PORT}
      - USER_ID=${USER_ID}
      - GROUP_ID=${GROUP_ID}
    command: ["/bin/sh", "/etc/caddy/start-caddy.sh"]

  trojan-certbot:
    image: certbot/certbot:latest
    container_name: trojan-certbot
    restart: unless-stopped
    volumes:
      - ./etc/certbot/conf:/etc/letsencrypt
      - ./etc/certbot/www:/var/www/certbot
      - ./etc/certbot/logs:/var/log/certbot
      - ./etc/certbot/renew-cert.sh:/usr/local/bin/renew-cert.sh:ro
      - ./etc/certbot/start-certbot.sh:/usr/local/bin/start-certbot.sh:ro
      - ./etc/certbot/crontab:/etc/cron.d/certbot:ro
      - ./etc/ssl:/etc/ssl/cloudflare
      - /var/run/docker.sock:/var/run/docker.sock
    environment:
      - DOMAIN=${DOMAIN}
      - CERTBOT_EMAIL=${CERTBOT_EMAIL}
      - SKIP_CERTBOT=${SKIP_CERTBOT}
      - CDN_PROVIDER=${CDN_PROVIDER}
    entrypoint: ["/bin/sh"]
    command: ["/usr/local/bin/start-certbot.sh"]
    depends_on:
      - trojan-caddy

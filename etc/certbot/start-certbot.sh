#!/bin/bash

# Certbot 服务启动脚本
# 负责初始化环境、立即刷新证书、启动定时任务

set -e

echo "=== Certbot 服务启动 ==="
echo "时间: $(date)"

# 检查是否需要跳过 certbot 证书签发
SKIP_CERTBOT="${SKIP_CERTBOT:-false}"
CDN_PROVIDER="${CDN_PROVIDER:-none}"

echo "SKIP_CERTBOT: ${SKIP_CERTBOT}"
echo "CDN_PROVIDER: ${CDN_PROVIDER}"

# 判断是否需要跳过证书签发
if [ "${SKIP_CERTBOT}" = "true" ] || [ "${CDN_PROVIDER}" = "cloudflare" ]; then
    echo "=== 证书签发已禁用 ==="
    echo "原因: SKIP_CERTBOT=${SKIP_CERTBOT}, CDN_PROVIDER=${CDN_PROVIDER}"
    echo "服务将进入待机模式，不执行证书签发操作"
    echo "如需手动放置证书文件，请将证书放置在 /etc/ssl/cloudflare/ 目录下"
    echo "证书文件名: cert.pem (证书链), key.pem (私钥)"

    # 进入无限循环保持容器运行，但不执行任何证书操作
    while true; do
        echo "$(date): Certbot 服务处于待机模式..."
        sleep 3600  # 每小时输出一次状态信息
    done
else
    echo "=== 证书签发已启用 ==="
    echo "将执行正常的证书签发和更新流程"

    # 安装必要的包
    # echo "安装必要的包..."
    apk add --no-cache docker-cli

    # 使用简单的循环代替 cron 守护进程，避免权限问题
    while true; do
        echo "$(date): 执行定期证书检查..."
        sh /usr/local/bin/renew-cert.sh
        sleep 3600  # 每小时检查一次
    done
fi

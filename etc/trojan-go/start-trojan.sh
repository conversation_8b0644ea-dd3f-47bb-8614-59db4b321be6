#!/bin/bash

# Trojan-Go 启动脚本
# 从环境变量生成配置文件并启动 trojan-go 服务

set -e

# 配置文件路径
TEMPLATE_FILE="/etc/trojan-go/config-template.json"
CONFIG_FILE="/etc/trojan-go/config.json"
LOG_FILE="/var/log/trojan/server.log"

# 默认环境变量
DEFAULT_DOMAIN="localhost"
DEFAULT_PASSWORD="your-password-here"
DEFAULT_WEBSOCKET_PATH="/websocket"
DEFAULT_LOCAL_PORT="443"
DEFAULT_FALLBACK_PORT="80"
DEFAULT_LOG_LEVEL="1"
DEFAULT_SSL_CERT="/etc/ssl/cloudflare/cert.pem"
DEFAULT_SSL_KEY="/etc/ssl/cloudflare/key.pem"

# 从环境变量获取配置，如果未设置则使用默认值
DOMAIN="${DOMAIN:-$DEFAULT_DOMAIN}"
TROJAN_PASSWORD="${TROJAN_PASSWORD:-$DEFAULT_PASSWORD}"
WEBSOCKET_PATH="${WEBSOCKET_PATH:-$DEFAULT_WEBSOCKET_PATH}"
LOCAL_PORT="${LOCAL_PORT:-$DEFAULT_LOCAL_PORT}"
FALLBACK_PORT="${FALLBACK_PORT:-$DEFAULT_FALLBACK_PORT}"
LOG_LEVEL="${LOG_LEVEL:-$DEFAULT_LOG_LEVEL}"
SSL_CERT="${SSL_CERT:-$DEFAULT_SSL_CERT}"
SSL_KEY="${SSL_KEY:-$DEFAULT_SSL_KEY}"

echo "=== Trojan-Go 启动脚本 ==="
echo "时间: $(date)"
echo "域名: $DOMAIN"
echo "WebSocket 路径: $WEBSOCKET_PATH"
echo "本地端口: $LOCAL_PORT"
echo "回落端口: $FALLBACK_PORT"
echo "SSL 证书: $SSL_CERT"
echo "SSL 私钥: $SSL_KEY"
echo ""

# 检查模板文件是否存在
if [ ! -f "$TEMPLATE_FILE" ]; then
    echo "错误: 配置模板文件不存在: $TEMPLATE_FILE"
    exit 1
fi

# 检查 SSL 证书文件
if [ ! -f "$SSL_CERT" ]; then
    echo "警告: SSL 证书文件不存在: $SSL_CERT"
fi

if [ ! -f "$SSL_KEY" ]; then
    echo "警告: SSL 私钥文件不存在: $SSL_KEY"
fi

# 创建日志目录
mkdir -p "$(dirname "$LOG_FILE")"

# 生成配置文件
echo "生成配置文件: $CONFIG_FILE"
cat "$TEMPLATE_FILE" | \
    sed "s|\${DOMAIN}|$DOMAIN|g" | \
    sed "s|\${TROJAN_PASSWORD}|$TROJAN_PASSWORD|g" | \
    sed "s|\${WEBSOCKET_PATH}|$WEBSOCKET_PATH|g" | \
    sed "s|\${LOCAL_PORT}|$LOCAL_PORT|g" | \
    sed "s|\${FALLBACK_PORT}|$FALLBACK_PORT|g" | \
    sed "s|\${LOG_LEVEL}|$LOG_LEVEL|g" | \
    sed "s|\${SSL_CERT}|$SSL_CERT|g" | \
    sed "s|\${SSL_KEY}|$SSL_KEY|g" > "$CONFIG_FILE"

# 验证配置文件格式
echo "验证配置文件格式..."
if command -v jq >/dev/null 2>&1; then
    if ! jq empty "$CONFIG_FILE" 2>/dev/null; then
        echo "错误: 配置文件 JSON 格式无效"
        exit 1
    fi
    echo "配置文件格式验证通过"
else
    echo "警告: jq 未安装，跳过 JSON 格式验证"
fi

# 显示生成的配置文件内容（隐藏密码）
echo ""
echo "生成的配置文件内容:"
if command -v jq >/dev/null 2>&1; then
    jq '.password = ["***HIDDEN***"]' "$CONFIG_FILE"
else
    cat "$CONFIG_FILE" | sed 's/"'$TROJAN_PASSWORD'"/"***HIDDEN***"/g'
fi

echo ""
echo "启动 trojan-go 服务..."
echo "配置文件: $CONFIG_FILE"
echo "日志文件: $LOG_FILE"
echo ""

# 设置日志文件权限（如果环境变量存在）
if [ -n "$USER_ID" ] && [ -n "$GROUP_ID" ]; then
    echo "设置日志文件权限为 $USER_ID:$GROUP_ID"
    touch "$LOG_FILE"
    chown "$USER_ID:$GROUP_ID" "$LOG_FILE"
    chmod 664 "$LOG_FILE"

    # 设置日志目录权限
    LOG_DIR=$(dirname "$LOG_FILE")
    chown "$USER_ID:$GROUP_ID" "$LOG_DIR"
    chmod 775 "$LOG_DIR"
fi

# 启动 trojan-go
exec trojan-go -config "$CONFIG_FILE"
